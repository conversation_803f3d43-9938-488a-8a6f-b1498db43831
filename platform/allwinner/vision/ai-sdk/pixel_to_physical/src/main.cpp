#include "../include/detection_processor.h"
#include "../include/data_type.h"
#include "../include/app_config.h"
#include "../include/logger.h"
#include <iostream>
#include <iomanip>
#include <string>
#include <vector>

void printUsage(const char* program_name) {
    std::cout << "用法: " << program_name << " [选项]" << std::endl;
    std::cout << "选项:" << std::endl;
    std::cout << "  --debug     启用调试模式，显示详细日志信息" << std::endl;
    std::cout << "  --info      设置日志级别为INFO（默认）" << std::endl;
    std::cout << "  --warning   设置日志级别为WARNING" << std::endl;
    std::cout << "  --error     设置日志级别为ERROR" << std::endl;
    std::cout << "  --help      显示此帮助信息" << std::endl;
}

int main(int argc, char* argv[]) {
    try {
        // 默认日志级别为INFO
        Logger::Level log_level = Logger::INFO;

        // 解析命令行参数
        for (int i = 1; i < argc; ++i) {
            std::string arg = argv[i];
            if (arg == "--debug") {
                log_level = Logger::DEBUG;
                std::cout << "启用调试模式" << std::endl;
            } else if (arg == "--info") {
                log_level = Logger::INFO;
                std::cout << "设置日志级别为INFO" << std::endl;
            } else if (arg == "--warning") {
                log_level = Logger::WARNING;
                std::cout << "设置日志级别为WARNING" << std::endl;
            } else if (arg == "--error") {
                log_level = Logger::ERROR;
                std::cout << "设置日志级别为ERROR" << std::endl;
            } else if (arg == "--help" || arg == "-h") {
                printUsage(argv[0]);
                return 0;
            } else {
                std::cerr << "未知参数: " << arg << std::endl;
                printUsage(argv[0]);
                return 1;
            }
        }

        // 设置日志级别
        Logger::setLevel(log_level);

        std::cout << "=== 像素到物理距离转换测试程序 ===" << std::endl;
        std::cout << std::endl;

        // 加载应用配置
        AppConfig config;

        // 尝试从JSON文件加载配置
        std::string json_config_path = "../config/app_config.json";
        if (config.loadFromJson(json_config_path)) {
            std::cout << "成功从JSON文件加载配置: " << json_config_path << std::endl;
        } else {
            std::cerr << "错误: 无法加载JSON配置文件: " << json_config_path << std::endl;
            std::cerr << "请确保配置文件存在且格式正确" << std::endl;
            return 1;
        }
        std::cout << std::endl;

        // 创建检测处理器实例
        DetectionProcessor processor;
        auto config_ptr = std::make_shared<AppConfig>(config);
        processor.initializeDistanceTable(config.distance_table_path, config_ptr);

        // 尝试加载JSON配置文件
        processor.setSizeRangesConfigFromJson(config.size_ranges_config);

        // 加载相机内参
        if (processor.initializeCameraIntrinsics(config.calib_intrix_path)) {
            std::cout << "成功加载相机内参: " << config.calib_intrix_path << std::endl;
        } else {
            std::cerr << "警告: 无法加载相机内参文件: " << config.calib_intrix_path << std::endl;
            std::cerr << "相机坐标转换功能将不可用" << std::endl;
        }

        // 测试1: 创建需要测距的检测结果 (flag = 1)
        std::cout << "=== 测试1: 测距标志位为1 ===" << std::endl;
        
        BBox bbox1;
        bbox1.xmin = 340; bbox1.ymin = 400; bbox1.xmax = 774; bbox1.ymax = 719;
        bbox1.label = 5; bbox1.score = 0.9f;
        float measure_flag1 = 1.0f;  // 测距标志位，1表示需要测距

        BBoxFlag bbox_flag1;
        bbox_flag1.box = bbox1;
        bbox_flag1.flag = measure_flag1;
        DetectionResult det1(bbox_flag1);

        std::cout << "输入检测框: (" << det1.bbox_flag.box.xmin << ", " << det1.bbox_flag.box.ymin << ", "
                  << det1.bbox_flag.box.xmax << ", " << det1.bbox_flag.box.ymax << ")" << std::endl;
        std::cout << "测距标志位: " << det1.bbox_flag.flag << std::endl;

        // 处理检测结果
        auto result1 = processor.processDetectionResult(det1);

        std::cout << "处理结果:" << std::endl;
        std::cout << "  物理距离: " << std::fixed << std::setprecision(2) << result1.physical_distance << " cm" << std::endl;
        std::cout << "  左下角距离: " << result1.left_distance << " cm" << std::endl;
        std::cout << "  右下角距离: " << result1.right_distance << " cm" << std::endl;
        std::cout << "  目标长度: " << result1.length << " cm" << std::endl;
        // 输出相机坐标系转换结果
        std::cout << std::endl;
        std::cout << "=== 相机坐标系转换结果 ===" << std::endl;
        if (result1.camera_coords.bottom_right.z > 0) {  // 检查是否有有效的相机坐标
            
            std::cout << "  左下角: ("
                      << result1.camera_coords.bottom_left.x << ", "
                      << result1.camera_coords.bottom_left.y << ", "
                      << result1.camera_coords.bottom_left.z << ") cm" << std::endl;
            std::cout << "  右下角: ("
                      << result1.camera_coords.bottom_right.x << ", "
                      << result1.camera_coords.bottom_right.y << ", "
                      << result1.camera_coords.bottom_right.z << ") cm" << std::endl;

             } 
        //     else {
        //     std::cout << "相机坐标转换未执行或失败" << std::endl;
        // }
        std::cout << std::endl;

        return 0;
    } catch (const std::exception& e) {
        std::cerr << "Error: " << e.what() << std::endl;
        return 1;
    }
}

