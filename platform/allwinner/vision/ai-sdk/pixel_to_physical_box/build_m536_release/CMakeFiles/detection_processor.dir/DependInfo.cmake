
# Consider dependencies only in project.
set(CMAKE_DEPENDS_IN_PROJECT_ONLY OFF)

# The set of languages for which implicit dependencies are needed:
set(CMAKE_DEPENDS_LANGUAGES
  )

# The set of dependency files which are needed:
set(CMAKE_DEPENDS_DEPENDENCY_FILES
  "/home/<USER>/panpan/code/tina-mr536-v1.2/platform/allwinner/vision/ai-sdk/pixel_to_physical/src/app_config.cpp" "CMakeFiles/detection_processor.dir/src/app_config.cpp.o" "gcc" "CMakeFiles/detection_processor.dir/src/app_config.cpp.o.d"
  "/home/<USER>/panpan/code/tina-mr536-v1.2/platform/allwinner/vision/ai-sdk/pixel_to_physical/src/camera_intrinsics.cpp" "CMakeFiles/detection_processor.dir/src/camera_intrinsics.cpp.o" "gcc" "CMakeFiles/detection_processor.dir/src/camera_intrinsics.cpp.o.d"
  "/home/<USER>/panpan/code/tina-mr536-v1.2/platform/allwinner/vision/ai-sdk/pixel_to_physical/src/detection_processor.cpp" "CMakeFiles/detection_processor.dir/src/detection_processor.cpp.o" "gcc" "CMakeFiles/detection_processor.dir/src/detection_processor.cpp.o.d"
  "/home/<USER>/panpan/code/tina-mr536-v1.2/platform/allwinner/vision/ai-sdk/pixel_to_physical/src/logger.cpp" "CMakeFiles/detection_processor.dir/src/logger.cpp.o" "gcc" "CMakeFiles/detection_processor.dir/src/logger.cpp.o.d"
  "/home/<USER>/panpan/code/tina-mr536-v1.2/platform/allwinner/vision/ai-sdk/pixel_to_physical/src/pixel_converter.cpp" "CMakeFiles/detection_processor.dir/src/pixel_converter.cpp.o" "gcc" "CMakeFiles/detection_processor.dir/src/pixel_converter.cpp.o.d"
  "/home/<USER>/panpan/code/tina-mr536-v1.2/platform/allwinner/vision/ai-sdk/pixel_to_physical/src/simple_json.cpp" "CMakeFiles/detection_processor.dir/src/simple_json.cpp.o" "gcc" "CMakeFiles/detection_processor.dir/src/simple_json.cpp.o.d"
  )

# Targets to which this target links.
set(CMAKE_TARGET_LINKED_INFO_FILES
  )

# Fortran module output directory.
set(CMAKE_Fortran_TARGET_MODULE_DIR "")
