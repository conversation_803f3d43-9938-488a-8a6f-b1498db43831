# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.22

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/panpan/code/tina-mr536-v1.2/platform/allwinner/vision/ai-sdk/pixel_to_physical

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/panpan/code/tina-mr536-v1.2/platform/allwinner/vision/ai-sdk/pixel_to_physical/build_m536_release

# Include any dependencies generated for this target.
include CMakeFiles/detection_processor.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/detection_processor.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/detection_processor.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/detection_processor.dir/flags.make

CMakeFiles/detection_processor.dir/src/detection_processor.cpp.o: CMakeFiles/detection_processor.dir/flags.make
CMakeFiles/detection_processor.dir/src/detection_processor.cpp.o: ../src/detection_processor.cpp
CMakeFiles/detection_processor.dir/src/detection_processor.cpp.o: CMakeFiles/detection_processor.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/panpan/code/tina-mr536-v1.2/platform/allwinner/vision/ai-sdk/pixel_to_physical/build_m536_release/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/detection_processor.dir/src/detection_processor.cpp.o"
	/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/aarch64-openwrt-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/detection_processor.dir/src/detection_processor.cpp.o -MF CMakeFiles/detection_processor.dir/src/detection_processor.cpp.o.d -o CMakeFiles/detection_processor.dir/src/detection_processor.cpp.o -c /home/<USER>/panpan/code/tina-mr536-v1.2/platform/allwinner/vision/ai-sdk/pixel_to_physical/src/detection_processor.cpp

CMakeFiles/detection_processor.dir/src/detection_processor.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/detection_processor.dir/src/detection_processor.cpp.i"
	/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/aarch64-openwrt-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/panpan/code/tina-mr536-v1.2/platform/allwinner/vision/ai-sdk/pixel_to_physical/src/detection_processor.cpp > CMakeFiles/detection_processor.dir/src/detection_processor.cpp.i

CMakeFiles/detection_processor.dir/src/detection_processor.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/detection_processor.dir/src/detection_processor.cpp.s"
	/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/aarch64-openwrt-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/panpan/code/tina-mr536-v1.2/platform/allwinner/vision/ai-sdk/pixel_to_physical/src/detection_processor.cpp -o CMakeFiles/detection_processor.dir/src/detection_processor.cpp.s

CMakeFiles/detection_processor.dir/src/pixel_converter.cpp.o: CMakeFiles/detection_processor.dir/flags.make
CMakeFiles/detection_processor.dir/src/pixel_converter.cpp.o: ../src/pixel_converter.cpp
CMakeFiles/detection_processor.dir/src/pixel_converter.cpp.o: CMakeFiles/detection_processor.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/panpan/code/tina-mr536-v1.2/platform/allwinner/vision/ai-sdk/pixel_to_physical/build_m536_release/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building CXX object CMakeFiles/detection_processor.dir/src/pixel_converter.cpp.o"
	/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/aarch64-openwrt-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/detection_processor.dir/src/pixel_converter.cpp.o -MF CMakeFiles/detection_processor.dir/src/pixel_converter.cpp.o.d -o CMakeFiles/detection_processor.dir/src/pixel_converter.cpp.o -c /home/<USER>/panpan/code/tina-mr536-v1.2/platform/allwinner/vision/ai-sdk/pixel_to_physical/src/pixel_converter.cpp

CMakeFiles/detection_processor.dir/src/pixel_converter.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/detection_processor.dir/src/pixel_converter.cpp.i"
	/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/aarch64-openwrt-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/panpan/code/tina-mr536-v1.2/platform/allwinner/vision/ai-sdk/pixel_to_physical/src/pixel_converter.cpp > CMakeFiles/detection_processor.dir/src/pixel_converter.cpp.i

CMakeFiles/detection_processor.dir/src/pixel_converter.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/detection_processor.dir/src/pixel_converter.cpp.s"
	/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/aarch64-openwrt-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/panpan/code/tina-mr536-v1.2/platform/allwinner/vision/ai-sdk/pixel_to_physical/src/pixel_converter.cpp -o CMakeFiles/detection_processor.dir/src/pixel_converter.cpp.s

CMakeFiles/detection_processor.dir/src/app_config.cpp.o: CMakeFiles/detection_processor.dir/flags.make
CMakeFiles/detection_processor.dir/src/app_config.cpp.o: ../src/app_config.cpp
CMakeFiles/detection_processor.dir/src/app_config.cpp.o: CMakeFiles/detection_processor.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/panpan/code/tina-mr536-v1.2/platform/allwinner/vision/ai-sdk/pixel_to_physical/build_m536_release/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building CXX object CMakeFiles/detection_processor.dir/src/app_config.cpp.o"
	/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/aarch64-openwrt-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/detection_processor.dir/src/app_config.cpp.o -MF CMakeFiles/detection_processor.dir/src/app_config.cpp.o.d -o CMakeFiles/detection_processor.dir/src/app_config.cpp.o -c /home/<USER>/panpan/code/tina-mr536-v1.2/platform/allwinner/vision/ai-sdk/pixel_to_physical/src/app_config.cpp

CMakeFiles/detection_processor.dir/src/app_config.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/detection_processor.dir/src/app_config.cpp.i"
	/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/aarch64-openwrt-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/panpan/code/tina-mr536-v1.2/platform/allwinner/vision/ai-sdk/pixel_to_physical/src/app_config.cpp > CMakeFiles/detection_processor.dir/src/app_config.cpp.i

CMakeFiles/detection_processor.dir/src/app_config.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/detection_processor.dir/src/app_config.cpp.s"
	/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/aarch64-openwrt-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/panpan/code/tina-mr536-v1.2/platform/allwinner/vision/ai-sdk/pixel_to_physical/src/app_config.cpp -o CMakeFiles/detection_processor.dir/src/app_config.cpp.s

CMakeFiles/detection_processor.dir/src/simple_json.cpp.o: CMakeFiles/detection_processor.dir/flags.make
CMakeFiles/detection_processor.dir/src/simple_json.cpp.o: ../src/simple_json.cpp
CMakeFiles/detection_processor.dir/src/simple_json.cpp.o: CMakeFiles/detection_processor.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/panpan/code/tina-mr536-v1.2/platform/allwinner/vision/ai-sdk/pixel_to_physical/build_m536_release/CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Building CXX object CMakeFiles/detection_processor.dir/src/simple_json.cpp.o"
	/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/aarch64-openwrt-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/detection_processor.dir/src/simple_json.cpp.o -MF CMakeFiles/detection_processor.dir/src/simple_json.cpp.o.d -o CMakeFiles/detection_processor.dir/src/simple_json.cpp.o -c /home/<USER>/panpan/code/tina-mr536-v1.2/platform/allwinner/vision/ai-sdk/pixel_to_physical/src/simple_json.cpp

CMakeFiles/detection_processor.dir/src/simple_json.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/detection_processor.dir/src/simple_json.cpp.i"
	/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/aarch64-openwrt-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/panpan/code/tina-mr536-v1.2/platform/allwinner/vision/ai-sdk/pixel_to_physical/src/simple_json.cpp > CMakeFiles/detection_processor.dir/src/simple_json.cpp.i

CMakeFiles/detection_processor.dir/src/simple_json.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/detection_processor.dir/src/simple_json.cpp.s"
	/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/aarch64-openwrt-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/panpan/code/tina-mr536-v1.2/platform/allwinner/vision/ai-sdk/pixel_to_physical/src/simple_json.cpp -o CMakeFiles/detection_processor.dir/src/simple_json.cpp.s

CMakeFiles/detection_processor.dir/src/logger.cpp.o: CMakeFiles/detection_processor.dir/flags.make
CMakeFiles/detection_processor.dir/src/logger.cpp.o: ../src/logger.cpp
CMakeFiles/detection_processor.dir/src/logger.cpp.o: CMakeFiles/detection_processor.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/panpan/code/tina-mr536-v1.2/platform/allwinner/vision/ai-sdk/pixel_to_physical/build_m536_release/CMakeFiles --progress-num=$(CMAKE_PROGRESS_5) "Building CXX object CMakeFiles/detection_processor.dir/src/logger.cpp.o"
	/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/aarch64-openwrt-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/detection_processor.dir/src/logger.cpp.o -MF CMakeFiles/detection_processor.dir/src/logger.cpp.o.d -o CMakeFiles/detection_processor.dir/src/logger.cpp.o -c /home/<USER>/panpan/code/tina-mr536-v1.2/platform/allwinner/vision/ai-sdk/pixel_to_physical/src/logger.cpp

CMakeFiles/detection_processor.dir/src/logger.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/detection_processor.dir/src/logger.cpp.i"
	/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/aarch64-openwrt-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/panpan/code/tina-mr536-v1.2/platform/allwinner/vision/ai-sdk/pixel_to_physical/src/logger.cpp > CMakeFiles/detection_processor.dir/src/logger.cpp.i

CMakeFiles/detection_processor.dir/src/logger.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/detection_processor.dir/src/logger.cpp.s"
	/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/aarch64-openwrt-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/panpan/code/tina-mr536-v1.2/platform/allwinner/vision/ai-sdk/pixel_to_physical/src/logger.cpp -o CMakeFiles/detection_processor.dir/src/logger.cpp.s

CMakeFiles/detection_processor.dir/src/camera_intrinsics.cpp.o: CMakeFiles/detection_processor.dir/flags.make
CMakeFiles/detection_processor.dir/src/camera_intrinsics.cpp.o: ../src/camera_intrinsics.cpp
CMakeFiles/detection_processor.dir/src/camera_intrinsics.cpp.o: CMakeFiles/detection_processor.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/panpan/code/tina-mr536-v1.2/platform/allwinner/vision/ai-sdk/pixel_to_physical/build_m536_release/CMakeFiles --progress-num=$(CMAKE_PROGRESS_6) "Building CXX object CMakeFiles/detection_processor.dir/src/camera_intrinsics.cpp.o"
	/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/aarch64-openwrt-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/detection_processor.dir/src/camera_intrinsics.cpp.o -MF CMakeFiles/detection_processor.dir/src/camera_intrinsics.cpp.o.d -o CMakeFiles/detection_processor.dir/src/camera_intrinsics.cpp.o -c /home/<USER>/panpan/code/tina-mr536-v1.2/platform/allwinner/vision/ai-sdk/pixel_to_physical/src/camera_intrinsics.cpp

CMakeFiles/detection_processor.dir/src/camera_intrinsics.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/detection_processor.dir/src/camera_intrinsics.cpp.i"
	/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/aarch64-openwrt-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/panpan/code/tina-mr536-v1.2/platform/allwinner/vision/ai-sdk/pixel_to_physical/src/camera_intrinsics.cpp > CMakeFiles/detection_processor.dir/src/camera_intrinsics.cpp.i

CMakeFiles/detection_processor.dir/src/camera_intrinsics.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/detection_processor.dir/src/camera_intrinsics.cpp.s"
	/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/aarch64-openwrt-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/panpan/code/tina-mr536-v1.2/platform/allwinner/vision/ai-sdk/pixel_to_physical/src/camera_intrinsics.cpp -o CMakeFiles/detection_processor.dir/src/camera_intrinsics.cpp.s

# Object files for target detection_processor
detection_processor_OBJECTS = \
"CMakeFiles/detection_processor.dir/src/detection_processor.cpp.o" \
"CMakeFiles/detection_processor.dir/src/pixel_converter.cpp.o" \
"CMakeFiles/detection_processor.dir/src/app_config.cpp.o" \
"CMakeFiles/detection_processor.dir/src/simple_json.cpp.o" \
"CMakeFiles/detection_processor.dir/src/logger.cpp.o" \
"CMakeFiles/detection_processor.dir/src/camera_intrinsics.cpp.o"

# External object files for target detection_processor
detection_processor_EXTERNAL_OBJECTS =

lib/libdistance_detection.so: CMakeFiles/detection_processor.dir/src/detection_processor.cpp.o
lib/libdistance_detection.so: CMakeFiles/detection_processor.dir/src/pixel_converter.cpp.o
lib/libdistance_detection.so: CMakeFiles/detection_processor.dir/src/app_config.cpp.o
lib/libdistance_detection.so: CMakeFiles/detection_processor.dir/src/simple_json.cpp.o
lib/libdistance_detection.so: CMakeFiles/detection_processor.dir/src/logger.cpp.o
lib/libdistance_detection.so: CMakeFiles/detection_processor.dir/src/camera_intrinsics.cpp.o
lib/libdistance_detection.so: CMakeFiles/detection_processor.dir/build.make
lib/libdistance_detection.so: CMakeFiles/detection_processor.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=/home/<USER>/panpan/code/tina-mr536-v1.2/platform/allwinner/vision/ai-sdk/pixel_to_physical/build_m536_release/CMakeFiles --progress-num=$(CMAKE_PROGRESS_7) "Linking CXX shared library lib/libdistance_detection.so"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/detection_processor.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/detection_processor.dir/build: lib/libdistance_detection.so
.PHONY : CMakeFiles/detection_processor.dir/build

CMakeFiles/detection_processor.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/detection_processor.dir/cmake_clean.cmake
.PHONY : CMakeFiles/detection_processor.dir/clean

CMakeFiles/detection_processor.dir/depend:
	cd /home/<USER>/panpan/code/tina-mr536-v1.2/platform/allwinner/vision/ai-sdk/pixel_to_physical/build_m536_release && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/panpan/code/tina-mr536-v1.2/platform/allwinner/vision/ai-sdk/pixel_to_physical /home/<USER>/panpan/code/tina-mr536-v1.2/platform/allwinner/vision/ai-sdk/pixel_to_physical /home/<USER>/panpan/code/tina-mr536-v1.2/platform/allwinner/vision/ai-sdk/pixel_to_physical/build_m536_release /home/<USER>/panpan/code/tina-mr536-v1.2/platform/allwinner/vision/ai-sdk/pixel_to_physical/build_m536_release /home/<USER>/panpan/code/tina-mr536-v1.2/platform/allwinner/vision/ai-sdk/pixel_to_physical/build_m536_release/CMakeFiles/detection_processor.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : CMakeFiles/detection_processor.dir/depend

