#include "../include/detection_processor.h"
#include "../include/data_type.h"
#include "../include/logger.h"
#include <iostream>
#include <iomanip>
#include <cmath>
#include <tuple>
#include <stdexcept>
#include <limits>

void DetectionProcessor::setSizeRangesConfigFromJson(const std::string& json_file) {
    try {
        size_ranges_config_ = SimpleJson::parseFile(json_file);
    } catch (const std::exception& e) {
        LOG_ERROR("加载JSON配置文件失败:", e.what());
        // 使用默认配置
        size_ranges_config_ = SimpleJson(SimpleJson::NULL_TYPE);
    }
}

void DetectionProcessor::setSizeRangesConfig(const SimpleJson& config) {
    size_ranges_config_ = config;
}

#ifdef HAVE_YAML_CPP
void DetectionProcessor::setSizeRangesConfig(const YAML::Node& config) {
    yaml_size_ranges_config_ = config;
}
#endif

void DetectionProcessor::initializeDistanceTable(const std::string& table_path, std::shared_ptr<AppConfig> config) {
    pixel_converter_ = std::make_unique<PixelConverter>(table_path, config);
    app_config_ = config;  // 保存AppConfig引用以便访问class_names
}

bool DetectionProcessor::initializeCameraIntrinsics(const std::string& intrinsics_file) {
    camera_intrinsics_ = std::make_unique<CameraIntrinsics>();
    bool success = false;

    // 根据文件扩展名判断是YAML还是JSON
    std::string ext = intrinsics_file.substr(intrinsics_file.find_last_of(".") + 1);
    if (ext == "json") {
        LOG_INFO("检测到JSON格式的相机内参文件");
        success = camera_intrinsics_->loadFromJsonFile(intrinsics_file);
    } else if (ext == "yaml" || ext == "yml") {
        LOG_INFO("检测到YAML格式的相机内参文件");
        success = camera_intrinsics_->loadFromYamlFile(intrinsics_file);
    } else {
        LOG_ERROR("不支持的相机内参文件格式:", ext);
        success = false;
    }

    if (success) {
        LOG_INFO("相机内参初始化成功");
    } else {
        LOG_ERROR("相机内参初始化失败");
        camera_intrinsics_.reset();
    }
    return success;
}



bool DetectionProcessor::isValidCoordinate(double x, double y) const {
    // 检查是否为无效区域标记 (0.000, 0.490) 或类似的值
    return !(std::abs(x) < 0.01 || std::abs(std::abs(y) - 0.49) < 0.1);
}

std::pair<double, double> DetectionProcessor::findNearestValidPoint(int pixel_x, int pixel_y, int max_search_radius) {
    if (!pixel_converter_) {
        LOG_ERROR("PixelConverter 未初始化");
        return std::make_pair(std::numeric_limits<double>::quiet_NaN(), std::numeric_limits<double>::quiet_NaN());
    }
    LOG_DEBUG("搜索像素点 (", pixel_x, ",", pixel_y, ") 周围的有效坐标, 最大半径:", max_search_radius);

    for (int radius = 1; radius <= max_search_radius; ++radius) {
        // 搜索以当前点为中心的正方形区域
        for (int dx = -radius; dx <= radius; ++dx) {
            for (int dy = -radius; dy <= radius; ++dy) {
                // 只检查边界点，避免重复搜索内部点
                if (std::abs(dx) != radius && std::abs(dy) != radius) {
                    continue;
                }

                int search_x = pixel_x + dx;
                int search_y = pixel_y + dy;

                // 检查搜索点是否在有效范围内
                if (pixel_converter_->isValidPixelCoordinate(search_x, search_y)) {
                    try {
                        auto [phys_x, phys_y] = pixel_converter_->queryPhysicalLocation(search_x, search_y);
                        if (isValidCoordinate(phys_x, phys_y)) {
                            LOG_DEBUG("找到有效点: 像素(", search_x, ",", search_y, ") -> 物理(", phys_x, ",", phys_y, "), 搜索半径:", radius);
                            return std::make_pair(phys_x, phys_y);
                        }
                    } catch (...) {
                        continue;
                    }
                }
            }
        }
    }
    LOG_WARNING("在半径", max_search_radius, "内未找到有效坐标点");
    return std::make_pair(std::numeric_limits<double>::quiet_NaN(), std::numeric_limits<double>::quiet_NaN());
}

std::pair<double, double> DetectionProcessor::getRobustPhysicalCoordinate(int pixel_x, int pixel_y) {
    if (!pixel_converter_) {
        LOG_ERROR("PixelConverter 未初始化");
        return std::make_pair(std::numeric_limits<double>::quiet_NaN(), std::numeric_limits<double>::quiet_NaN());
    }
    try {
        // 首先尝试直接查询
        auto [phys_x, phys_y] = pixel_converter_->queryPhysicalLocation(pixel_x, pixel_y);

        if (isValidCoordinate(phys_x, phys_y)) {
            return std::make_pair(phys_x, phys_y);
        }

        // LOG_WARNING("像素点 (", pixel_x, ",", pixel_y, ") 在无效标定区域，物理坐标: (", phys_x, ",", phys_y, ")");

        // 策略1: 搜索最近的有效点
        auto [valid_x, valid_y] = findNearestValidPoint(pixel_x, pixel_y);
        if (!std::isnan(valid_x)) {
            // LOG_WARNING("使用最近的有效点: (", valid_x, ",", valid_y, ")");
            return std::make_pair(valid_x, valid_y);
        } else {
            return std::make_pair(std::numeric_limits<double>::quiet_NaN(), std::numeric_limits<double>::quiet_NaN());
        }

    } catch (const std::exception& e) {
        LOG_ERROR("获取物理坐标失败:", e.what());
        return std::make_pair(std::numeric_limits<double>::quiet_NaN(), std::numeric_limits<double>::quiet_NaN());
    }
}

std::tuple<double, double, double> DetectionProcessor::calculateTargetSize(const BBox& bbox) {
    if (!pixel_converter_) {
        LOG_ERROR("PixelConverter 未初始化");
        return std::make_tuple(0.0, 0.0, 0.0);
    }
    try {
        // 使用鲁棒方法获取物理坐标
        auto [z1, y1] = getRobustPhysicalCoordinate(bbox.xmin, bbox.ymax);  // bottom left
        auto [z2, y2] = getRobustPhysicalCoordinate(bbox.xmax, bbox.ymax);  // bottom right

        // 计算长度
        double bottom_length = std::sqrt(std::pow(z2 - z1, 2) + std::pow(y2 - y1, 2));

        LOG_DEBUG("左下角像素坐标 (", bbox.xmin, ",", bbox.ymax, ") -> 物理坐标 (", z1, ",", y1, ")");
        LOG_DEBUG("右下角像素坐标 (", bbox.xmax, ",", bbox.ymax, ") -> 物理坐标 (", z2, ",", y2, ")");
        LOG_DEBUG("下边长度:", bottom_length, "cm");

            return std::make_tuple(bottom_length, z1, z2);

    } catch (const std::exception& e) {
        LOG_ERROR("计算目标尺寸失败:", e.what());
        return std::make_tuple(0.0, 0.0, 0.0);
    }
}

bool DetectionProcessor::isSizeReasonableForLabel(const std::string& label, double size_y) const {
    try {
        // 首先尝试使用JSON配置
        if (!size_ranges_config_.isNull() && size_ranges_config_.hasKey("objects")) {
            const SimpleJson& objects = size_ranges_config_["objects"];
            const SimpleJson* object_config = nullptr;

            if (objects.hasKey(label)) {
                object_config = &objects[label];
            } else if (size_ranges_config_.hasKey("default")) {
                object_config = &size_ranges_config_["default"];
            }

            if (object_config && !object_config->isNull()) {
                double config_max_size = (*object_config)["max_size"].asDouble();
                double config_min_size = (*object_config)["min_size"].asDouble();
                std::string description = (*object_config)["description"].asString();

                LOG_DEBUG(label, "长度:", size_y, "cm, 配置范围:", config_min_size, "-", config_max_size, "cm");

                if (size_y > config_max_size || size_y < config_min_size) {
                    LOG_WARNING(description);
                    return false;
                }
                return true;
            }
        }

#ifdef HAVE_YAML_CPP
        // 如果JSON配置不可用，尝试YAML配置
        if (yaml_size_ranges_config_["objects"][label]) {
            YAML::Node object_config = yaml_size_ranges_config_["objects"][label];
            double config_max_size = object_config["max_size"].as<double>();
            double config_min_size = object_config["min_size"].as<double>();
            std::string description = object_config["description"].as<std::string>();

            LOG_DEBUG(label, "长度:", size_y, "cm (YAML配置检查), 范围:", config_min_size, "-", config_max_size);

            if (size_y > config_max_size || size_y < config_min_size) {
                LOG_WARNING(description);
                return false;
            }
            return true;
        } else if (yaml_size_ranges_config_["default"]) {
            YAML::Node object_config = yaml_size_ranges_config_["default"];
            double config_max_size = object_config["max_size"].as<double>();
            double config_min_size = object_config["min_size"].as<double>();
            std::string description = object_config["description"].as<std::string>();

            if (size_y > config_max_size || size_y < config_min_size) {
                LOG_WARNING(label, "长度:", size_y, "cm,", description);
                return false;
            }
            return true;
        }
#endif

        // 如果没有配置文件，使用默认的尺寸检查逻辑
        LOG_DEBUG(label, "长度:", size_y, "cm (使用默认检查)");

        // 简单的默认范围检查
        if (size_y > 200.0 || size_y < 5.0) {
            LOG_WARNING("物体尺寸超出合理范围 (5-200cm)");
            return false;
        }
        return true;

    } catch (const std::exception& e) {
        LOG_ERROR("检查尺寸范围失败:", e.what());
        // 出错时使用默认检查
        if (size_y > 200.0 || size_y < 5.0) {
            return false;
        }
        return true;
    }
}

std::tuple<bool, double, double, double, std::string> DetectionProcessor::isDetectionReasonable(DetectionResult& det) {
    if (!pixel_converter_) {
        LOG_ERROR("PixelConverter 未初始化");
        return std::make_tuple(false, 0.0, 0.0, 0.0, "converter not initialized");
    }

    // 性能优化：预计算边界框尺寸，避免重复计算
    const int bbox_width = det.bbox_flag.box.xmax - det.bbox_flag.box.xmin;
    const int bbox_height = det.bbox_flag.box.ymax - det.bbox_flag.box.ymin;

    // 检查检测框是否在图像范围内
    if (!pixel_converter_->isValidPixelCoordinate(det.bbox_flag.box.xmin, det.bbox_flag.box.ymax) ||
        !pixel_converter_->isValidPixelCoordinate(det.bbox_flag.box.xmax, det.bbox_flag.box.ymax)) {
        // LOG_WARNING("检测框超出测距范围");
        return std::make_tuple(false, 0.0, 0.0, 0.0, "out of image range");
    }

    // 性能优化：使用预计算的尺寸，避免重复减法运算
    const int box_area = bbox_width * bbox_height;
    if (box_area < 10) {
        LOG_WARNING("检测框面积过小 (", box_area, "像素),不进行测距");
        return std::make_tuple(false, 0.0, 0.0, 0.0, "box area is small");
    }

    // 计算目标尺寸
    LOG_DEBUG("开始计算目标尺寸...");
    auto [size_y, z1, z2] = calculateTargetSize(det.bbox_flag.box);
    LOG_DEBUG("目标尺寸计算完成: size_y=", size_y, ", z1=", z1, ", z2=", z2);

    // 性能优化：缓存距离值，避免重复访问
    const double distance = z1;
    const int x_max = pixel_converter_->getXMax(); // 缓存配置值

    if (distance >= x_max) {
        LOG_WARNING("目标距离过远 (", distance, "cm)");
        return std::make_tuple(false, 0.0, 0.0, 0.0, "distance far");
    }

    // 打印目标尺寸
    // std::cout << "\n目标尺寸:" << std::endl;
    // std::cout << "长度: " << std::fixed << std::setprecision(3) << size_y << " cm" << std::endl;

    // 检查尺寸与标签的匹配
    // 将int类型的label转换为string类型
    std::string label_str = "";
    if (app_config_ && det.bbox_flag.box.label >= 0 && det.bbox_flag.box.label < static_cast<int>(app_config_->class_names.size())) {
        label_str = app_config_->class_names[det.bbox_flag.box.label];
    }
    if (!isSizeReasonableForLabel(label_str, size_y)) {
        return std::make_tuple(false, 0.0, 0.0, 0.0, "size false");
    }

    // 性能优化：使用空字符串字面量，避免临时对象创建
    return std::make_tuple(true, z1, z2, size_y, "");
}

bool DetectionProcessor::convertPixelToCameraCoordinates(DetectionResult& det) {
    // if (!camera_intrinsics_ || !camera_intrinsics_->isInitialized()) {
    //     // LOG_WARNING("相机内参未初始化，跳过相机坐标转换");
    //     return false;
    // }

    // if (det.physical_distance <= 0) {
    //     // LOG_WARNING("物理距离无效，无法进行相机坐标转换");
    //     return false;
    // }

    // 将物理距离从厘米转换为米（如果相机内参使用米作为单位）
    // 这里假设physical_distance是厘米，相机坐标系也使用厘米
    double depth_left = det.left_distance;
    double depth_right = det.right_distance;

    // // 计算bbox中心点的像素坐标
    // int center_x = (det.bbox.x1 + det.bbox.x2) / 2;
    // int center_y = (det.bbox.y1 + det.bbox.y2) / 2;

    // // 转换bbox中心点到相机坐标系
    // auto [cam_x, cam_y, cam_z] = camera_intrinsics_->pixelToCamera(center_x, center_y, depth);
    // det.camera_coords.center = CameraPoint(cam_x, cam_y, cam_z);

    // // 转换bbox四个角点到相机坐标系
    // // 左上角 (x1, y1)
    // auto [cam_x1, cam_y1, cam_z1] = camera_intrinsics_->pixelToCamera(det.bbox.x1, det.bbox.y1, depth);
    // det.camera_coords.top_left = CameraPoint(cam_x1, cam_y1, cam_z1);

    // // 右上角 (x2, y1)
    // auto [cam_x2, cam_y2, cam_z2] = camera_intrinsics_->pixelToCamera(det.bbox.x2, det.bbox.y1, depth);
    // det.camera_coords.top_right = CameraPoint(cam_x2, cam_y2, cam_z2);

    // 右下角 (xmax, ymax)
    auto [cam_x3, cam_y3, cam_z3] = camera_intrinsics_->pixelToCamera(det.bbox_flag.box.xmax, det.bbox_flag.box.ymax, depth_right);
    det.camera_coords.bottom_right = CameraPoint(cam_x3, cam_y3, cam_z3);
    // det.camera_x3 = cam_x3;
    // det.camera_y3 = cam_y3;
    // det.camera_z3 = cam_z3;

    // 左下角 (xmin, ymax)
    auto [cam_x4, cam_y4, cam_z4] = camera_intrinsics_->pixelToCamera(det.bbox_flag.box.xmin, det.bbox_flag.box.ymax, depth_left);
    det.camera_coords.bottom_left = CameraPoint(cam_x4, cam_y4, cam_z4);
    // det.camera_x4 = cam_x4;
    // det.camera_y4 = cam_y4;
    // det.camera_z4 = cam_z4;

    LOG_DEBUG("左下(", det.camera_coords.bottom_left.x, ",", det.camera_coords.bottom_left.y, ",", det.camera_coords.bottom_left.z, "), "
              "右下(", det.camera_coords.bottom_right.x, ",", det.camera_coords.bottom_right.y, ",", det.camera_coords.bottom_right.z, ")");

    return true;
}

DetectionResult DetectionProcessor::processDetectionResult(DetectionResult& det) {
    // 获取标签名称用于日志
    std::string label_name = "";
    if (app_config_ && det.bbox_flag.box.label >= 0 && det.bbox_flag.box.label < static_cast<int>(app_config_->class_names.size())) {
        label_name = app_config_->class_names[det.bbox_flag.box.label];
    }
    LOG_DEBUG("处理检测结果: 标签ID:", det.bbox_flag.box.label, ", 标签名称:", label_name, ", 置信度:", det.bbox_flag.box.score, ", 测距标志:", det.bbox_flag.flag);

    // 检查是否需要测距
    if (!det.shouldMeasureDistance()) {
        LOG_INFO("测距标志为0，跳过测距处理");
        return det;
    }

    auto [is_reasonable, z1, z2, size_y, des] = isDetectionReasonable(det);

    det.length = size_y;
    // det.des = des;
    // 更新检测结果的物理距离
    det.left_distance = z1;
    det.right_distance = z2;
    det.physical_distance = (z1 + z2) / 2;  // 使用左下角和右下角距离的平均值

    // 转换像素坐标到相机坐标系
    if (camera_intrinsics_ && camera_intrinsics_->isInitialized()) {
        if (is_reasonable) {
            convertPixelToCameraCoordinates(det);
            LOG_INFO("已将检测框转换为相机坐标系");
        } 
        // else {
        //     LOG_WARNING("相机坐标转换失败");
        // }
    } else {
        LOG_DEBUG("相机内参未初始化，跳过相机坐标转换");
    }

    return det;
}

