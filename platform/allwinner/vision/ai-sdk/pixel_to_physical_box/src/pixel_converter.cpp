#include "../include/pixel_converter.h"
#include "../include/logger.h"
#include <stdexcept>

PixelConverter::PixelConverter(const std::string& table_path, std::shared_ptr<AppConfig> config)
    : table_path_(table_path)
    , config_(config)
{
    // 确保config不为空，如果为空则抛出异常
    if (!config_) {
        throw std::invalid_argument("PixelConverter初始化失败：AppConfig不能为空");
    }

    // 从配置中获取常量
    IMGX_RANGE = config_->imgx_range;
    IMGY_RANGE = config_->imgy_range;
    X_MIN = config_->X_MIN;
    X_MAX = config_->X_MAX;
    Y_MIN = config_->Y_MIN;
    Y_MAX = config_->Y_MAX;

    // 计算表格尺寸
    TABLE_WIDTH = IMGX_RANGE.second - IMGX_RANGE.first + 1;
    TABLE_HEIGHT = IMGY_RANGE.second - IMGY_RANGE.first + 1;

    loadTable();
}

bool PixelConverter::isValidPixelCoordinate(int pixel_x, int pixel_y) const {
    return pixel_x >= IMGX_RANGE.first && pixel_x <= IMGX_RANGE.second &&
           pixel_y >= IMGY_RANGE.first && pixel_y <= IMGY_RANGE.second;
}

void PixelConverter::loadTable() {
    std::ifstream table_file(table_path_, std::ios::binary);
    if (!table_file.is_open()) {
        throw std::runtime_error("无法打开距离表文件: " + table_path_);
    }

    // 读取整个文件
    table_file.seekg(0, std::ios::end);
    size_t file_size = table_file.tellg();
    table_file.seekg(0, std::ios::beg);

    std::vector<uint8_t> data(file_size);
    table_file.read(reinterpret_cast<char*>(data.data()), file_size);
    table_file.close();

    // 将数据重新整形为(x, y)对的数组
    table_data_.clear();
    table_data_.reserve(file_size / 2);

    for (size_t i = 0; i < file_size; i += 2) {
        table_data_.emplace_back(data[i], data[i + 1]);
    }
}

double PixelConverter::mapValueBack(uint8_t value, double min_val, double max_val) {
    return min_val + (value / 255.0) * (max_val - min_val);
}

std::pair<double, double> PixelConverter::queryPhysicalLocation(int pixel_x, int pixel_y) {
    // 检查输入像素坐标是否在有效范围内
    if (!isValidPixelCoordinate(pixel_x, pixel_y)) {
        throw std::out_of_range("像素坐标超出有效范围: 坐标(" + std::to_string(pixel_x) + "," + std::to_string(pixel_y) +
            "), 有效范围: X[" + std::to_string(IMGX_RANGE.first) + "," + std::to_string(IMGX_RANGE.second) +
            "], Y[" + std::to_string(IMGY_RANGE.first) + "," + std::to_string(IMGY_RANGE.second) + "]");
    }

    // 计算在表格中的索引
    int row_offset = pixel_y - IMGY_RANGE.first;
    int col_offset = pixel_x - IMGX_RANGE.first;
    int index = row_offset * TABLE_WIDTH + col_offset;

    if (index >= static_cast<int>(table_data_.size())) {
        throw std::out_of_range("坐标索引无效: 坐标(" + std::to_string(pixel_x) + "," + std::to_string(pixel_y) +
            "), 索引" + std::to_string(index) + ", 表格大小" + std::to_string(table_data_.size()));
    }

    uint8_t x_value = table_data_[index].first;
    uint8_t y_value = table_data_[index].second;

    // 检查无效值
    if (x_value == 0 && y_value == 0) {
        LOG_WARNING("在像素位置 (", pixel_x, ",", pixel_y, ") 检测到坐标 (0,0), 该区域可能无效或未校准");
    }

    // 将值映射回物理坐标
    double physical_x = mapValueBack(x_value, X_MIN, X_MAX);
    double physical_y = mapValueBack(y_value, Y_MIN, Y_MAX);

    return std::make_pair(physical_x, physical_y);
}



// Getter methods
std::pair<int, int> PixelConverter::getImgxRange() const {
    return IMGX_RANGE;
}

std::pair<int, int> PixelConverter::getImgyRange() const {
    return IMGY_RANGE;
}

int PixelConverter::getXMin() const {
    return X_MIN;
}

int PixelConverter::getXMax() const {
    return X_MAX;
}

int PixelConverter::getYMin() const {
    return Y_MIN;
}

int PixelConverter::getYMax() const {
    return Y_MAX;
}