cmake_minimum_required(VERSION 3.10)
project(detection_processor)

# 设置 C++ 标准
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)


# 显示构建类型
message(STATUS "Build type: ${CMAKE_BUILD_TYPE}")
if(CMAKE_BUILD_TYPE STREQUAL "Debug")
    message(STATUS "Debug mode: LOG_DEBUG and LOG_INFO will be visible")
else()
    message(STATUS "Release mode: Only LOG_WARNING and LOG_ERROR will be visible")
endif()

# 设置输出目录
set(CMAKE_LIBRARY_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib)
set(CMAKE_RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin)
set(CMAKE_ARCHIVE_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib)

# # 查找OpenCV (可选)
# option(USE_OPENCV "Enable OpenCV support" ON)

# if(USE_OPENCV)
#     find_package(OpenCV REQUIRED)
#     if(OpenCV_FOUND)
#         message(STATUS "Found OpenCV version: ${OpenCV_VERSION}")
#         message(STATUS "OpenCV include dirs: ${OpenCV_INCLUDE_DIRS}")
#         message(STATUS "OpenCV libraries: ${OpenCV_LIBS}")
#         add_definitions(-DUSE_OPENCV)
#     else()
#         message(FATAL_ERROR "OpenCV not found!")
#     endif()
# else()
#     message(STATUS "OpenCV support disabled")
# endif()

# 添加头文件目录
include_directories(${CMAKE_CURRENT_SOURCE_DIR}/include)
# if(USE_OPENCV AND OpenCV_FOUND)
#     include_directories(${OpenCV_INCLUDE_DIRS})
# endif()

# 添加源文件
set(SOURCES
    src/detection_processor.cpp
    src/pixel_converter.cpp
    src/app_config.cpp
    src/simple_json.cpp
    src/logger.cpp
    src/camera_intrinsics.cpp
)

# 强制禁用 YAML-CPP 支持以简化交叉编译

# 创建动态库
add_library(detection_processor SHARED ${SOURCES})

# 链接OpenCV库 (如果启用)
# if(USE_OPENCV AND OpenCV_FOUND)
#     target_link_libraries(detection_processor ${OpenCV_LIBS})
# endif()

# 设置库的属性
set_target_properties(detection_processor PROPERTIES
    OUTPUT_NAME "distance_detection"
    PREFIX "lib"
)

# 不使用yaml-cpp，使用默认配置
message(STATUS "Building without YAML support for cross-compilation")

# 添加测试程序
add_executable(distance_detection src/main.cpp)
target_link_libraries(distance_detection PRIVATE detection_processor)

# 安装规则
install(TARGETS detection_processor distance_detection
    LIBRARY DESTINATION lib
    RUNTIME DESTINATION bin
    PUBLIC_HEADER DESTINATION include
)

# 导出头文件
install(FILES
    include/detection_processor.h
    include/pixel_converter.h
    include/app_config.h
    include/data_type.h
    include/simple_json.h
    include/camera_intrinsics.h
    include/logger.h
    DESTINATION include/detection_processor
)