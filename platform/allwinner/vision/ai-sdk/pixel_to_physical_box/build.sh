#!/bin/bash

# 通用构建脚本
# 用法: ./build.sh [debug|release]

BUILD_TYPE=${1:-release}

echo "=== 构建项目 (类型: $BUILD_TYPE) ==="

# 创建build目录
if [ ! -d "build" ]; then
    mkdir build
fi

cd build

# 根据构建类型设置CMAKE参数
case $BUILD_TYPE in
    debug)
        echo "配置Debug模式..."
        cmake -DCMAKE_BUILD_TYPE=Debug ..
        ;;
    release)
        echo "配置Release模式..."
        cmake -DCMAKE_BUILD_TYPE=Release ..
        ;;
    *)
        echo "未知构建类型: $BUILD_TYPE"
        echo "支持的类型: debug, release"
        exit 1
        ;;
esac

# 编译
echo "开始编译..."
make

if [ $? -eq 0 ]; then
    echo "=== 构建完成 ==="
    echo "运行程序: ./bin/distance_detection.sh [debug|info|warning|error]"
else
    echo "=== 构建失败 ==="
    exit 1
fi
