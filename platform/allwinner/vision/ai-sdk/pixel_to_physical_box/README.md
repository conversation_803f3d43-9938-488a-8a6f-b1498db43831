# Pixel to Physical Distance Converter

这个项目是一个用于将图像中的像素坐标转换为实际物理距离的工具。它主要用于计算机视觉和机器人应用中，帮助计算检测到的物体到相机的实际距离。

## 功能特点

- 支持将像素坐标转换为物理距离
- 可配置的物体尺寸范围
- 基于 json 的配置文件系统
- 支持多种物体类型的检测结果处理

## 项目结构

```
pixel_to_physical/
├── include/                 # 头文件目录
│   ├── detection_processor.h
│   └── pixel_converter.h
├── src/                    # 源代码目录
│   └── main.cpp
├── config/                 # 配置文件目录
│   └── size_ranges.yaml
├── test/                   # 测试文件目录
├── CMakeLists.txt         # CMake 构建文件
└── distance_table         # 距离查找表
```

## 依赖项

- C++11 或更高版本
- CMake 3.10 或更高版本
- yaml-cpp 库
- OpenCV（可选，用于图像处理）

## 安装说明

1. 克隆仓库：
```bash
git clone [repository_url]
cd pixel_to_physical
```

2. 构建项目：

### 本地编译
```bash
# Debug模式
./build.sh debug

# Release模式
./build.sh release
```

### 交叉编译（MR536平台）
```bash
# 使用M536工具链进行交叉编译
# Debug模式
./cross_compile_m536.sh debug

# Release模式
./cross_compile_m536.sh release

# 使用0-toolchains工具链进行交叉编译
# Debug模式
./cross_compile_0toolchain.sh debug

# Release模式
./cross_compile_0toolchain.sh release
```

3. 运行程序：
```bash
# 调试模式（显示所有日志）
cd build/bin
./distance_detection debug

# 信息模式（显示INFO及以上级别日志）
cd build/bin
./distance_detection info

# 警告模式（只显示WARNING和ERROR）
cd build/bin
./distance_detection warning

# 错误模式（只显示ERROR）
cd build/bin
./distance_detection error
```

## 使用方法

1. 配置 `config/size_ranges.json` 文件，设置不同物体类型的尺寸范围。

2. 在代码中使用：
```cpp
#include <detection_processor.h>

// 创建检测处理器实例
DetectionProcessor processor;

// 加载配置文件
config.loadFromJson(json_config_path)
processor.setSizeRangesConfigFromJson(config.size_ranges_config);


// 处理检测结果
DetectionResult det = {
    {x1, y1, x2, y2},  // 边界框坐标
    "object_type",     // 物体类型
    confidence         // 置信度
    physical_distance; // 物理距离 （左下角+右下角距离的平均值）
    left_distance;      //左下角距离
    right_distance;     //右下角距离
    length;             //目标长度
};

processor.processDetectionResult(&det);
```

## 配置说明

在 `config/size_ranges.json` 文件中，您可以配置不同物体类型的尺寸范围。配置文件格式如下：

```yaml
object_types:
  trash_bin:
    max_size: 20
    min_size: 50
```

## 交叉编译输出

交叉编译完成后，编译产物会保存在以下目录：

- **M536工具链**:
  - Debug模式: `output_cross_m536_debug/`
  - Release模式: `output_cross_m536_release/`

- **0-toolchains工具链**:
  - Debug模式: `output_cross_debug/`
  - Release模式: `output_cross_release/`

每个输出目录包含：
- `libdetection_processor.so*` - 动态库文件
- `distance_detection` - 测试程序

## 注意事项

- 确保 `distance_table` 文件存在且格式正确
- 检测结果的边界框坐标需要按照 [x1, y1, x2, y2] 的格式提供
- 物体类型需要在配置文件中定义
- Debug版本包含调试信息，文件较大；Release版本经过优化，文件较小

## 许可证

[添加许可证信息]

## 贡献

欢迎提交 Issue 和 Pull Request 来帮助改进这个项目。 