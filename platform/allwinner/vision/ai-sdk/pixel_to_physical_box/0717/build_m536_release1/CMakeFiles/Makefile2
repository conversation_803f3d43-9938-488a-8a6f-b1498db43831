# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.22

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/panpan/code/tina-mr536-v1.2/platform/allwinner/vision/ai-sdk/pixel_to_physical

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/panpan/code/tina-mr536-v1.2/platform/allwinner/vision/ai-sdk/pixel_to_physical/build_m536_release

#=============================================================================
# Directory level rules for the build root directory

# The main recursive "all" target.
all: CMakeFiles/detection_processor.dir/all
all: CMakeFiles/distance_detection.dir/all
.PHONY : all

# The main recursive "preinstall" target.
preinstall:
.PHONY : preinstall

# The main recursive "clean" target.
clean: CMakeFiles/detection_processor.dir/clean
clean: CMakeFiles/distance_detection.dir/clean
.PHONY : clean

#=============================================================================
# Target rules for target CMakeFiles/detection_processor.dir

# All Build rule for target.
CMakeFiles/detection_processor.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/detection_processor.dir/build.make CMakeFiles/detection_processor.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/detection_processor.dir/build.make CMakeFiles/detection_processor.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/panpan/code/tina-mr536-v1.2/platform/allwinner/vision/ai-sdk/pixel_to_physical/build_m536_release/CMakeFiles --progress-num=1,2,3,4,5,6,7 "Built target detection_processor"
.PHONY : CMakeFiles/detection_processor.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/detection_processor.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/panpan/code/tina-mr536-v1.2/platform/allwinner/vision/ai-sdk/pixel_to_physical/build_m536_release/CMakeFiles 7
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/detection_processor.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/panpan/code/tina-mr536-v1.2/platform/allwinner/vision/ai-sdk/pixel_to_physical/build_m536_release/CMakeFiles 0
.PHONY : CMakeFiles/detection_processor.dir/rule

# Convenience name for target.
detection_processor: CMakeFiles/detection_processor.dir/rule
.PHONY : detection_processor

# clean rule for target.
CMakeFiles/detection_processor.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/detection_processor.dir/build.make CMakeFiles/detection_processor.dir/clean
.PHONY : CMakeFiles/detection_processor.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/distance_detection.dir

# All Build rule for target.
CMakeFiles/distance_detection.dir/all: CMakeFiles/detection_processor.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/distance_detection.dir/build.make CMakeFiles/distance_detection.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/distance_detection.dir/build.make CMakeFiles/distance_detection.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/panpan/code/tina-mr536-v1.2/platform/allwinner/vision/ai-sdk/pixel_to_physical/build_m536_release/CMakeFiles --progress-num=8,9 "Built target distance_detection"
.PHONY : CMakeFiles/distance_detection.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/distance_detection.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/panpan/code/tina-mr536-v1.2/platform/allwinner/vision/ai-sdk/pixel_to_physical/build_m536_release/CMakeFiles 9
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/distance_detection.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/panpan/code/tina-mr536-v1.2/platform/allwinner/vision/ai-sdk/pixel_to_physical/build_m536_release/CMakeFiles 0
.PHONY : CMakeFiles/distance_detection.dir/rule

# Convenience name for target.
distance_detection: CMakeFiles/distance_detection.dir/rule
.PHONY : distance_detection

# clean rule for target.
CMakeFiles/distance_detection.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/distance_detection.dir/build.make CMakeFiles/distance_detection.dir/clean
.PHONY : CMakeFiles/distance_detection.dir/clean

#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

