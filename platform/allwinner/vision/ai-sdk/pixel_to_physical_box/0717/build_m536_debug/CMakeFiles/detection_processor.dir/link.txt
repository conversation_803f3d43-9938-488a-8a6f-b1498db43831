/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/bin/aarch64-openwrt-linux-gnu-g++ -fPIC -O0 -g3 -pipe -march=armv8-a -mtune=cortex-a53 -fno-caller-saves -fno-plt -fPIC -std=c++17 -Wno-psabi -g -L/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/aarch64-openwrt-linux-gnu/lib -Wl,-rpath-link=/home/<USER>/panpan/code/tina-mr536-v1.2/prebuilt/m536_toolchain-sunxi-glibc-gcc-1320/toolchain/aarch64-openwrt-linux-gnu/lib -shared -Wl,-soname,libdistance_detection.so.1 -o lib/libdistance_detection.so.1.0.0 CMakeFiles/detection_processor.dir/src/detection_processor.cpp.o CMakeFiles/detection_processor.dir/src/pixel_converter.cpp.o CMakeFiles/detection_processor.dir/src/app_config.cpp.o CMakeFiles/detection_processor.dir/src/simple_json.cpp.o CMakeFiles/detection_processor.dir/src/logger.cpp.o CMakeFiles/detection_processor.dir/src/camera_intrinsics.cpp.o 
